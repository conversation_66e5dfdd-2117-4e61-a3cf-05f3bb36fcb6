import { describe, it, expect } from 'vitest';
import { createBudgetHierarchy, calculateSubtotal, type BudgetHierarchyNode } from '$lib/budget_utils';
import type { Tables } from '$lib/database.types';

// Helper function to create a WBS item
function createWbsItem(
	id: string,
	parentId: string | null,
	code: string,
	description: string,
	level: number,
): Tables<'wbs_library_item'> {
	return {
		wbs_library_item_id: id,
		wbs_library_id: 1,
		level,
		in_level_code: code.split('.').pop() || code,
		parent_item_id: parentId,
		code,
		description,
		cost_scope: null,
		item_type: 'Standard',
		client_id: null,
		project_id: null,
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z',
	};
}

// Helper function to create a budget snapshot line item
function createSnapshotItem(
	id: number,
	wbsItemId: string,
	quantity: number,
	unitRate: number,
	factor?: number,
): Tables<'budget_snapshot_line_item'> {
	return {
		budget_snapshot_line_item_id: id,
		budget_snapshot_id: 1,
		wbs_library_item_id: wbsItemId,
		quantity,
		unit: 'each',
		material_rate: unitRate,
		labor_rate: null,
		productivity_per_hour: null,
		unit_rate_manual_override: false,
		unit_rate: unitRate,
		factor: factor ?? 1,
		remarks: null,
		cost_certainty: null,
		design_certainty: null,
		created_at: '2024-01-01T00:00:00Z',
	};
}

describe('Budget Hierarchy', () => {
	describe('calculateSubtotal', () => {
		it('calculates subtotal correctly with factor', () => {
			const snapshotData = createSnapshotItem(1, 'item1', 10, 100, 1.5);
			const result = calculateSubtotal(snapshotData);
			expect(result).toBe(1500); // 10 * 100 * 1.5
		});

		it('calculates subtotal correctly without factor', () => {
			const snapshotData = createSnapshotItem(1, 'item1', 5, 200);
			const result = calculateSubtotal(snapshotData);
			expect(result).toBe(1000); // 5 * 200 * 1
		});

		it('returns 0 for undefined snapshot data', () => {
			const result = calculateSubtotal(undefined);
			expect(result).toBe(0);
		});
	});

	describe('createBudgetHierarchy', () => {
		it('creates a simple hierarchy with parent and child', () => {
			const wbsItems = [
				createWbsItem('parent-1', null, '1', 'Parent Item', 1),
				createWbsItem('child-1', 'parent-1', '1.1', 'Child Item', 2),
			];

			const snapshotItems = [
				createSnapshotItem(1, 'child-1', 2, 50), // subtotal: 100
			];

			const hierarchy = createBudgetHierarchy(wbsItems, snapshotItems);

			expect(hierarchy).toHaveLength(1);
			const parent = hierarchy[0];
			expect(parent.id).toBe('parent-1');
			expect(parent.data.code).toBe('1');
			expect(parent.value).toBe(100); // Should roll up child value
			expect(parent.children).toHaveLength(1);

			const child = parent.children![0];
			expect(child.id).toBe('child-1');
			expect(child.data.code).toBe('1.1');
			expect(child.value).toBe(100); // 2 * 50 * 1
			expect(child.data.subtotal).toBe(100);
		});

		it('creates hierarchy with multiple levels', () => {
			const wbsItems = [
				createWbsItem('root', null, '1', 'Root', 1),
				createWbsItem('level2-1', 'root', '1.1', 'Level 2 Item 1', 2),
				createWbsItem('level2-2', 'root', '1.2', 'Level 2 Item 2', 2),
				createWbsItem('level3-1', 'level2-1', '1.1.1', 'Level 3 Item 1', 3),
				createWbsItem('level3-2', 'level2-1', '1.1.2', 'Level 3 Item 2', 3),
			];

			const snapshotItems = [
				createSnapshotItem(1, 'level3-1', 1, 100), // subtotal: 100
				createSnapshotItem(2, 'level3-2', 2, 150), // subtotal: 300
				createSnapshotItem(3, 'level2-2', 1, 200), // subtotal: 200
			];

			const hierarchy = createBudgetHierarchy(wbsItems, snapshotItems);

			expect(hierarchy).toHaveLength(1);
			const root = hierarchy[0];
			expect(root.id).toBe('root');
			expect(root.value).toBe(600); // 100 + 300 + 200
			expect(root.children).toHaveLength(2);

			const level2_1 = root.children!.find(c => c.id === 'level2-1');
			const level2_2 = root.children!.find(c => c.id === 'level2-2');

			expect(level2_1).toBeDefined();
			expect(level2_1!.value).toBe(400); // 100 + 300
			expect(level2_1!.children).toHaveLength(2);

			expect(level2_2).toBeDefined();
			expect(level2_2!.value).toBe(200);
			expect(level2_2!.children).toHaveLength(0);
		});

		it('handles items without snapshot data', () => {
			const wbsItems = [
				createWbsItem('parent-1', null, '1', 'Parent Item', 1),
				createWbsItem('child-1', 'parent-1', '1.1', 'Child with data', 2),
				createWbsItem('child-2', 'parent-1', '1.2', 'Child without data', 2),
			];

			const snapshotItems = [
				createSnapshotItem(1, 'child-1', 3, 100), // subtotal: 300
			];

			const hierarchy = createBudgetHierarchy(wbsItems, snapshotItems);

			expect(hierarchy).toHaveLength(1);
			const parent = hierarchy[0];
			expect(parent.value).toBe(300); // Only from child-1
			expect(parent.children).toHaveLength(2);

			const childWithData = parent.children!.find(c => c.id === 'child-1');
			const childWithoutData = parent.children!.find(c => c.id === 'child-2');

			expect(childWithData!.value).toBe(300);
			expect(childWithData!.data.snapshotData).toBeDefined();

			expect(childWithoutData!.value).toBe(0);
			expect(childWithoutData!.data.snapshotData).toBeUndefined();
		});

		it('handles multiple root nodes', () => {
			const wbsItems = [
				createWbsItem('root-1', null, '1', 'Root 1', 1),
				createWbsItem('root-2', null, '2', 'Root 2', 1),
				createWbsItem('child-1', 'root-1', '1.1', 'Child 1', 2),
			];

			const snapshotItems = [
				createSnapshotItem(1, 'child-1', 2, 50), // subtotal: 100
				createSnapshotItem(2, 'root-2', 1, 300), // subtotal: 300
			];

			const hierarchy = createBudgetHierarchy(wbsItems, snapshotItems);

			expect(hierarchy).toHaveLength(2);

			const root1 = hierarchy.find(r => r.id === 'root-1');
			const root2 = hierarchy.find(r => r.id === 'root-2');

			expect(root1).toBeDefined();
			expect(root1!.value).toBe(100);
			expect(root1!.children).toHaveLength(1);

			expect(root2).toBeDefined();
			expect(root2!.value).toBe(300);
			expect(root2!.children).toHaveLength(0);
		});
	});
});
