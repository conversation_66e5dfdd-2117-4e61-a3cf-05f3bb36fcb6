<script lang="ts">
	import BudgetSnapshotNode from '$lib/components/budget-snapshot-node.svelte';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import { formatCurrency, formatPercentage } from '$lib/utils';
	import { SvelteSet } from 'svelte/reactivity';
	import type { EnhancedWbsItemTree } from '$lib/budget_utils';

	let {
		node,
		indent,
		expanded,
		toggle,
	}: {
		node: EnhancedWbsItemTree;
		indent: number;
		expanded: SvelteSet<string>;
		toggle: (nodeId: string) => void;
	} = $props();

	// ─── Derived reactive state ───
	// true if this node is in the expanded set
	const isOpen = $derived(!expanded.has(node.nodeId));

	// Called when the caret is clicked:
	function onToggle() {
		toggle(node.nodeId);
	}
</script>

<div
	class="group hover:bg-muted/20 relative col-span-[13] grid grid-cols-subgrid border-b py-3 pr-8 {node
		.children.length > 0
		? 'font-medium'
		: ''}"
>
	<div class="col-span-1 flex items-center px-2">
		{#if node.children.length > 0}
			<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
				{#if isOpen}
					<CaretDownIcon class="size-4" />
				{:else}
					<CaretRightIcon class="size-4" />
				{/if}
			</button>
			<span class="ml-2">{node.code}</span>
		{:else}
			<span class="ml-6">{node.code}</span>
		{/if}
	</div>

	<div class="col-span-2 px-2" style="padding-left: {0.5 + indent * 0.75}rem">
		{node.description}
	</div>
	<!-- Show total cost for nodes with children or no budget items -->
	{#if node.children.length > 0 || node.budgetItems.length === 0}
		<div class="col-span-[11] col-start-4 grid grid-cols-subgrid">
			<div class="col-span-7 px-2 text-right">
				{node.budgetItems[0]?.factor && node.budgetItems[0].factor !== 1
					? node.budgetItems[0].factor
					: ''}
			</div>
			<div class="col-span-1 px-2 text-right">
				{formatCurrency(node.totalCost)}
			</div>
			<div class="col-span-1 px-2 text-right"></div>
			<div class="col-span-1 px-2 text-right"></div>
		</div>
	{/if}
	<!-- Display budget items if any exist -->
	{#each node.budgetItems as budgetItem (budgetItem.budget_line_item_id)}
		{#if budgetItem.quantity || budgetItem.unit || budgetItem.unit_rate}
			<div class="col-span-[11] col-start-4 grid grid-cols-subgrid">
				<!-- Quantity, Unit -->
				<div class="col-span-1 px-2 text-right">
					{budgetItem.quantity}
				</div>
				<div class="col-span-1 px-2">
					{budgetItem.unit ?? ''}
				</div>

				<!-- Rate Calculation -->
				<div class="col-span-1 px-2 text-right">
					{#if !budgetItem.unit_rate_manual_override}
						{formatCurrency(budgetItem.material_rate)}
					{/if}
				</div>
				<div class="col-span-1 px-2 text-right">
					{#if !budgetItem.unit_rate_manual_override}
						{formatCurrency(budgetItem.labor_rate ?? 0)}
					{/if}
				</div>
				<div class="col-span-1 px-2 text-right">
					{#if !budgetItem.unit_rate_manual_override}
						{formatCurrency(budgetItem.productivity_per_hour ?? 0)}
					{/if}
				</div>
				<div class="col-span-1 px-2 text-right">
					{formatCurrency(budgetItem.unit_rate)}
				</div>

				<div class="col-span-1 px-2 text-right">
					{budgetItem.factor ?? ''}
				</div>

				<!-- Subtotal, Certainties -->
				<div class="col-span-1 px-2 text-right">
					{formatCurrency(budgetItem.quantity * budgetItem.unit_rate * (budgetItem.factor ?? 1))}
				</div>
				<div class="col-span-1 px-2 text-center">
					{budgetItem.cost_certainty != null ? formatPercentage(budgetItem.cost_certainty) : ''}
				</div>
				<div class="col-span-1 px-2 text-center">
					{budgetItem.design_certainty != null ? formatPercentage(budgetItem.design_certainty) : ''}
				</div>
			</div>
		{/if}
	{/each}
</div>

<!-- Only render children when this node is open -->
{#if isOpen}
	{#each node.children as child (child.nodeId)}
		<BudgetSnapshotNode node={child} indent={indent + 1} {expanded} {toggle} />
	{/each}
{/if}

<style>
	.col-span-1 {
		align-self: start;
		justify-self: stretch;
		width: 6rem;
	}

	.col-span-2 {
		align-self: start;
		width: 12rem;
	}
</style>
