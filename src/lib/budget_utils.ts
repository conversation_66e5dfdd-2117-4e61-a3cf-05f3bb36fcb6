import type { Tables } from '$lib/database.types';
import type { UpdateBudgetItem } from './schemas/project';
import { stratify } from 'd3-hierarchy';

// 1) Raw data shape coming from supabase.from('wbs_library_item').select(...)
export interface RawNode extends Tables<'wbs_library_item'> {
	budget_line_item_current: Tables<'budget_line_item_current'>[];
}

// Budget snapshot related interfaces
export interface BudgetSnapshotLineItem extends Tables<'budget_snapshot_line_item'> {
	wbs_library_item?: Tables<'wbs_library_item'>;
}

export interface BudgetSnapshot extends Tables<'budget_snapshot'> {
	project_stage?: {
		name: string;
		stage_order: number;
		stage: number | null;
		project_id: string;
	};
}

// 2) Node augmented with its computed costs and children
export interface NodeWithCosts extends RawNode {
	children: NodeWithCosts[];
	directCost: number;
	childrenCost: number;
	totalCost: number;
}

export interface EnhancedWbsItemTree extends Tables<'wbs_library_item'> {
	budgetItems: RawNode['budget_line_item_current'];
	directCost: number;
	childrenCost: number;
	totalCost: number;
	children: EnhancedWbsItemTree[];
	nodeId: string; // e.g. `node-${wbs_library_item_id}`
}

// New interfaces for budget snapshot hierarchy
export interface WbsItemWithSnapshotData extends Tables<'wbs_library_item'> {
	snapshotData?: Tables<'budget_snapshot_line_item'>;
	subtotal: number;
	value: number; // Used by stratify for automatic totaling
}

export interface BudgetHierarchyNode {
	id: string;
	parentId: string | null;
	data: WbsItemWithSnapshotData;
	value: number;
	children?: BudgetHierarchyNode[];
	depth: number;
}

export function buildBudgetTree(raw: RawNode[]): EnhancedWbsItemTree[] {
	// A) Clone each raw node into an EnhancedWbsItemTree skeleton
	const map = new Map<string, EnhancedWbsItemTree>();
	raw.forEach((r) => {
		map.set(r.wbs_library_item_id, {
			...r, // all wbs_library_item columns
			budgetItems: r.budget_line_item_current,
			directCost: 0,
			childrenCost: 0,
			totalCost: 0,
			children: [],
			nodeId: `node-${r.wbs_library_item_id}`,
		});
	});

	// B) Hook up parent ↔ child
	const roots: EnhancedWbsItemTree[] = [];
	map.forEach((node) => {
		if (node.parent_item_id && map.has(node.parent_item_id)) {
			map.get(node.parent_item_id)!.children.push(node);
		} else {
			roots.push(node);
		}
	});

	// C) Compute costs bottom-up
	function computeCosts(node: EnhancedWbsItemTree) {
		// directCost = sum(quantity * unit_rate)
		node.directCost = node.budgetItems.reduce(
			(sum, bi) => sum + bi.quantity * bi.unit_rate * (bi.factor ?? 1),
			0,
		);

		// first compute children, accumulate their totalCost
		node.children.forEach((child) => {
			computeCosts(child);
			node.childrenCost += child.totalCost;
		});

		// total = direct + children
		node.totalCost = node.directCost + node.childrenCost;
	}
	roots.forEach(computeCosts);

	return roots;
}

export function calculateUnitRate(
	item: Partial<RawNode['budget_line_item_current'][number]>,
): number {
	if (item.unit_rate_manual_override) {
		if (!item.unit_rate) {
			item.unit_rate = 0;
		}
		// If unit rate is manually overridden, return the manual value
		return item.unit_rate;
	}

	const materialCost = item.material_rate || 0;
	const laborCost = item.labor_rate || 0;
	const productivity = item.productivity_per_hour || 0;

	// If productivity is provided, calculate labor cost per unit
	const laborCostPerUnit = productivity > 0 ? laborCost / productivity : 0;

	return materialCost + laborCostPerUnit;
}

/**
 * Calculate subtotal for a budget item using the formula:
 * subtotal = quantity * unit_rate * (factor ?? 1)
 */
export function calculateSubtotal(
	snapshotData: Tables<'budget_snapshot_line_item'> | undefined,
): number {
	if (!snapshotData) return 0;
	return (snapshotData.quantity || 0) * (snapshotData.unit_rate || 0) * (snapshotData.factor ?? 1);
}

/**
 * Create hierarchical budget structure using stratify for budget snapshots
 */
export function createBudgetHierarchy(
	wbsItems: Tables<'wbs_library_item'>[],
	snapshotItems: Tables<'budget_snapshot_line_item'>[],
): BudgetHierarchyNode[] {
	// Create a map of snapshot data by WBS item ID
	const snapshotMap = new Map<string, Tables<'budget_snapshot_line_item'>>();
	snapshotItems.forEach((item) => {
		snapshotMap.set(item.wbs_library_item_id, item);
	});

	// Transform WBS items to include snapshot data and calculated values
	const itemsWithData: WbsItemWithSnapshotData[] = wbsItems.map((item) => {
		const snapshotData = snapshotMap.get(item.wbs_library_item_id);
		const subtotal = calculateSubtotal(snapshotData);

		return {
			...item,
			snapshotData,
			subtotal,
			value: subtotal, // stratify will use this for automatic totaling
		};
	});

	// Use stratify to create hierarchical structure
	const stratifyFn = stratify<WbsItemWithSnapshotData>()
		.id((d) => d.wbs_library_item_id)
		.parentId((d) => d.parent_item_id);

	const root = stratifyFn(itemsWithData);

	// Calculate values bottom-up (manual rollup since d3 stratify doesn't have .value() method)
	function calculateValues(node: any): number {
		let totalValue = node.data.value;
		if (node.children) {
			for (const child of node.children) {
				totalValue += calculateValues(child);
			}
		}
		node.value = totalValue;
		return totalValue;
	}

	// Calculate values for all nodes
	calculateValues(root);

	// Convert d3-hierarchy nodes to our interface
	function convertNode(d3Node: any): BudgetHierarchyNode {
		return {
			id: d3Node.id,
			parentId: d3Node.parent?.id || null,
			data: d3Node.data,
			value: d3Node.value || 0,
			depth: d3Node.depth,
			children: d3Node.children?.map(convertNode) || [],
		};
	}

	return root.children?.map(convertNode) || [];
}

// Enhanced BudgetLineItem with UI state
export interface BudgetLineItem extends Partial<UpdateBudgetItem> {
	budget_line_item_id?: number;
	project_id: string;
	wbs_library_item_id: string;
	quantity: number;
	unit: string | null;
	material_rate: number;
	labor_rate: number | null;
	productivity_per_hour: number | null;
	unit_rate_manual_override: boolean;
	unit_rate: number;
	remarks: string | null;
	cost_certainty: number | null;
	design_certainty: number | null;
	created_at?: string;
	updated_at?: string;
	// UI-specific fields
	wbs_code?: string;
	description?: string;
	subtotal?: number;
	extension?: number;
	isParent?: boolean;
	level?: number;
}
