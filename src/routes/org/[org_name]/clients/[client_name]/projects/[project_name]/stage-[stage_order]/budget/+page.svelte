<script lang="ts">
	import { SvelteSet } from 'svelte/reactivity';
	import { useSidebar } from '$lib/components/ui/sidebar';
	import BudgetSnapshotNode from '$lib/components/budget-snapshot-node.svelte';

	const { data } = $props();
	const sidebar = useSidebar();

	// State for expanding/collapsing nodes
	let expandedNodeIds = new SvelteSet<string>();

	// Toggle expanded/collapsed state of a node
	function toggleNodeExpanded(nodeId: string) {
		if (expandedNodeIds.has(nodeId)) {
			expandedNodeIds.delete(nodeId);
		} else {
			expandedNodeIds.add(nodeId);
		}
	}
</script>

<div class="overflow-hidden p-4">
	<div class="mb-4 flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold">Budget Snapshot - {data.stage.name}</h1>
			{#if data.hasSnapshots && data.latestSnapshot}
				<p class="text-muted-foreground">
					Snapshot from {new Date(data.latestSnapshot.freeze_date).toLocaleDateString()}
					{#if data.latestSnapshot.freeze_reason}
						- {data.latestSnapshot.freeze_reason}
					{/if}
				</p>
			{/if}
		</div>
	</div>

	{#if !data.hasSnapshots}
		<div class="flex flex-col items-center justify-center p-8 text-center">
			<h2 class="mb-2 text-lg font-semibold">No Budget Snapshots</h2>
			<p class="text-muted-foreground">
				No budget snapshots have been created for this project stage yet.
			</p>
		</div>
	{:else}
		<div
			class="-mr-4 -ml-4 overflow-scroll tabular-nums"
			style={`max-width: ${
				sidebar.open
					? 'calc(100vw - var(--sidebar-width) - 1rem)'
					: 'calc(100vw - var(--sidebar-width-icon) - 1rem)'
			}; max-height: calc(100vh - 11.5rem); position: relative;`}
		>
			<table class="budget-table w-full border-collapse">
				<thead>
					<tr class="bg-muted border-t border-b text-sm font-medium">
						<th class="w-24 py-2 pr-2 pl-8 text-left">WBS Code</th>
						<th class="w-48 px-2 text-left">Description</th>
						<th class="w-20 px-2 text-right">Quantity</th>
						<th class="w-16 px-2 text-left">Unit</th>
						<th class="w-24 border-b px-2 text-center">Rate Calculation</th>
						<th class="w-24 px-2 text-right">Material Rate</th>
						<th class="w-24 px-2 text-right">Labor Rate</th>
						<th class="w-24 px-2 text-right">Productivity</th>
						<th class="w-24 px-2 text-right">Unit Rate</th>
						<th class="w-16 px-2 text-right">Factor</th>
						<th class="w-24 px-2 text-right">Subtotal</th>
						<th class="w-24 px-2 text-right">Cost Certainty</th>
						<th class="w-24 px-2 text-right">Design Certainty</th>
					</tr>
					<tr class="bg-muted text-sm font-medium">
						<th colspan="4"></th>
						<th class="w-24 px-2 text-right">Material Rate</th>
						<th class="w-24 px-2 text-right">Labor Rate</th>
						<th class="w-24 px-2 text-right">Productivity</th>
						<th class="w-24 px-2 text-right">Unit Rate</th>
						<th colspan="4"></th>
					</tr>
				</thead>
				<tbody>
					{#if data.budgetHierarchy.length > 0}
						{#each data.budgetHierarchy as rootNode (rootNode.id)}
							<BudgetSnapshotNode
								node={rootNode}
								indent={0}
								expanded={expandedNodeIds}
								toggle={toggleNodeExpanded}
							/>
						{/each}
					{/if}
				</tbody>
			</table>
		</div>
	{/if}
</div>

<style>
	.budget-table {
		width: max-content;
		min-width: 100%;
	}

	.budget-table th {
		white-space: nowrap;
	}

	.overflow-scroll {
		padding-block: 0.5rem;
	}
</style>
