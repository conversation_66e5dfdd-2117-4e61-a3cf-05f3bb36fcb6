<script lang="ts">
	import { SvelteSet } from 'svelte/reactivity';
	import { useSidebar } from '$lib/components/ui/sidebar';
	import BudgetSnapshotNode from '$lib/components/budget-snapshot-node.svelte';

	const { data } = $props();
	const sidebar = useSidebar();

	// State for expanding/collapsing nodes
	let expandedNodeIds = new SvelteSet<string>();

	// Toggle expanded/collapsed state of a node
	function toggleNodeExpanded(nodeId: string) {
		if (expandedNodeIds.has(nodeId)) {
			expandedNodeIds.delete(nodeId);
		} else {
			expandedNodeIds.add(nodeId);
		}
	}
</script>

<div class="overflow-hidden p-4">
	<div class="mb-4 flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold">Budget Snapshot - {data.stage.name}</h1>
			{#if data.hasSnapshots && data.latestSnapshot}
				<p class="text-muted-foreground">
					Snapshot from {new Date(data.latestSnapshot.freeze_date).toLocaleDateString()}
					{#if data.latestSnapshot.freeze_reason}
						- {data.latestSnapshot.freeze_reason}
					{/if}
				</p>
			{/if}
		</div>
	</div>

	{#if !data.hasSnapshots}
		<div class="flex flex-col items-center justify-center p-8 text-center">
			<h2 class="mb-2 text-lg font-semibold">No Budget Snapshots</h2>
			<p class="text-muted-foreground">
				No budget snapshots have been created for this project stage yet.
			</p>
		</div>
	{:else}
		<div
			class="-mr-4 -ml-4 overflow-scroll tabular-nums"
			style={`max-width: ${
				sidebar.open
					? 'calc(100vw - var(--sidebar-width) - 1rem)'
					: 'calc(100vw - var(--sidebar-width-icon) - 1rem)'
			}; max-height: calc(100vh - 11.5rem); position: relative;`}
		>
			<div class="budget grid auto-cols-min grid-cols-12 gap-0">
				<!-- Grid Header -->
				<div
					class="bg-muted col-span-[13] grid grid-cols-subgrid items-stretch border-t border-b py-2 pr-8 text-sm font-medium"
				>
					<div class="col-span-1 pr-2 pl-8">WBS Code</div>
					<div class="col-span-2 px-2">Description</div>
					<div class="col-span-1 px-2 text-right">Quantity</div>
					<div class="col-span-1 px-2">Unit</div>
					<div class="col-span-4 grid grid-cols-subgrid gap-1 text-center">
						<div class="col-span-4 border-b">Rate Calculation</div>
						<div class="col-span-1 px-2 text-right">Material Rate</div>
						<div class="col-span-1 px-2 text-right">Labor Rate</div>
						<div class="col-span-1 px-2 text-right">Productivity</div>
						<div class="col-span-1 px-2 text-right">Unit Rate</div>
					</div>
					<div class="col-span-1 px-2 text-right">Factor</div>
					<div class="col-span-1 px-2 text-right">Subtotal</div>
					<div class="col-span-1 px-2 text-right">Cost Certainty</div>
					<div class="col-span-1 px-2 text-right">Design Certainty</div>
				</div>

				{#if data.wbsTree.length > 0}
					{#each data.wbsTree as rootNode (rootNode.nodeId)}
						<BudgetSnapshotNode
							node={rootNode}
							indent={0}
							expanded={expandedNodeIds}
							toggle={toggleNodeExpanded}
						/>
					{/each}
				{/if}
			</div>
		</div>
	{/if}
</div>

<style>
	.budget {
		width: max-content;
		justify-items: stretch;
		align-items: start;

		& .col-span-1 {
			align-self: end;
			width: 6rem;
		}
		& .col-span-2 {
			align-self: end;
			max-width: 12rem;
		}
	}

	.overflow-scroll {
		padding-block: 0.5rem;
	}
</style>
